import os
import shutil

def clear_tts_cache():
    cache_paths = [
        os.path.expanduser("~/.local/share/tts"),
        os.path.expanduser("~/.cache/tts"),
        os.path.join(os.getcwd(), ".tts_cache"),
        os.path.expanduser("~/AppData/Local/tts"),  # Windows specific
        os.path.expanduser("~/AppData/Roaming/tts")  # Windows specific
    ]
    
    for cache_path in cache_paths:
        if os.path.exists(cache_path):
            try:
                shutil.rmtree(cache_path)
                print(f"✓ Cleared {cache_path}")
            except Exception as e:
                print(f"✗ Could not clear {cache_path}: {e}")
        else:
            print(f"- {cache_path} (not found)")

if __name__ == "__main__":
    print("Clearing TTS cache...")
    clear_tts_cache()
    print("Done!")