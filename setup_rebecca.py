#!/usr/bin/env python3
"""
Rebecca Setup Script
Ensures all dependencies and models are properly installed
"""

import os
import sys
import subprocess
import shutil
import json
from pathlib import Path

def run_command(cmd, description=""):
    """Run a command and handle errors"""
    print(f"[SETUP] {description}...")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"[ERROR] {description} failed:")
            print(result.stderr)
            return False
        print(f"[SUCCESS] {description} completed")
        return True
    except Exception as e:
        print(f"[ERROR] {description} failed: {e}")
        return False

def check_python_version():
    """Check Python version compatibility"""
    print("[SETUP] Checking Python version...")
    version = sys.version_info
    if version.major != 3 or version.minor < 8:
        print(f"[ERROR] Python 3.8+ required, found {version.major}.{version.minor}")
        return False
    print(f"[SUCCESS] Python {version.major}.{version.minor}.{version.micro} OK")
    return True

def clear_tts_cache():
    """Clear TTS cache to fix corrupted downloads"""
    print("[SETUP] Clearing TTS cache...")
    cache_paths = [
        os.path.expanduser("~/.local/share/tts"),
        os.path.expanduser("~/.cache/tts"),
        os.path.join(os.getcwd(), ".tts_cache")
    ]
    
    for cache_path in cache_paths:
        if os.path.exists(cache_path):
            try:
                shutil.rmtree(cache_path)
                print(f"[SUCCESS] Cleared {cache_path}")
            except Exception as e:
                print(f"[WARNING] Could not clear {cache_path}: {e}")

def install_dependencies():
    """Install required packages"""
    print("[SETUP] Installing dependencies...")
    
    # Core dependencies
    deps = [
        "psutil",
        "numpy==1.22.0", 
        "opencv-python",
        "mss",
        "torch==2.5.1",
        "torchvision==0.20.1",
        "ultralytics==8.2.40",
        "websocket-client",
        "transformers==4.31.0",  # Compatible version for TTS
        "TTS==0.22.0"
    ]
    
    for dep in deps:
        if not run_command(f"pip install {dep}", f"Installing {dep}"):
            return False
    
    return True

def test_tts():
    """Test TTS functionality"""
    print("[SETUP] Testing TTS...")
    
    test_script = '''
import warnings
warnings.filterwarnings("ignore")

try:
    from TTS.api import TTS
    print("[TEST] TTS import successful")
    
    # Test simple model
    tts = TTS(model_name="tts_models/en/ljspeech/tacotron2-DDC", gpu=False)
    print("[TEST] TTS model loaded successfully")
    
    # Test synthesis
    tts.tts_to_file(text="Hello, this is a test.", file_path="test_output.wav")
    print("[TEST] TTS synthesis successful")
    
    import os
    if os.path.exists("test_output.wav"):
        os.remove("test_output.wav")
        print("[TEST] TTS test completed successfully")
    else:
        print("[ERROR] TTS test failed - no output file")
        
except Exception as e:
    print(f"[ERROR] TTS test failed: {e}")
    '''
    
    with open("test_tts.py", "w") as f:
        f.write(test_script)
    
    success = run_command("python test_tts.py", "Testing TTS")
    
    # Cleanup
    if os.path.exists("test_tts.py"):
        os.remove("test_tts.py")
    
    return success

def create_memory_file():
    """Create initial memory file"""
    print("[SETUP] Creating memory file...")
    
    initial_memory = {
        "age": 14,
        "gender": "female", 
        "name": "Rebecca",
        "birthday": "June 5th",
        "favorite_color": "Purple"
    }
    
    try:
        with open("mem.json", "w", encoding="utf-8") as f:
            json.dump(initial_memory, f, indent=2, ensure_ascii=False)
        print("[SUCCESS] Memory file created")
        return True
    except Exception as e:
        print(f"[ERROR] Could not create memory file: {e}")
        return False

def check_model_file():
    """Check if GGUF model file exists"""
    print("[SETUP] Checking for GGUF model...")
    
    model_file = os.environ.get("GGUF_MODEL_FILE", "mistral-7b-instruct-v0.2.Q4_K_S.gguf")
    
    if os.path.exists(model_file):
        print(f"[SUCCESS] Found model file: {model_file}")
        return True
    else:
        print(f"[WARNING] Model file not found: {model_file}")
        print("[INFO] Rebecca will work without local model (responses will be limited)")
        return True  # Not critical

def main():
    """Main setup function"""
    print("=" * 50)
    print("REBECCA SETUP SCRIPT")
    print("=" * 50)
    
    steps = [
        ("Python Version Check", check_python_version),
        ("Clear TTS Cache", clear_tts_cache),
        ("Install Dependencies", install_dependencies),
        ("Create Memory File", create_memory_file),
        ("Check Model File", check_model_file),
        ("Test TTS", test_tts)
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        print(f"\n[STEP] {step_name}")
        if not step_func():
            failed_steps.append(step_name)
    
    print("\n" + "=" * 50)
    print("SETUP COMPLETE")
    print("=" * 50)
    
    if failed_steps:
        print(f"[WARNING] Some steps failed: {', '.join(failed_steps)}")
        print("[INFO] Rebecca may still work with limited functionality")
    else:
        print("[SUCCESS] All setup steps completed successfully!")
    
    print("\nTo start Rebecca, run: python Rebecca.py")
    print("=" * 50)

if __name__ == "__main__":
    main()