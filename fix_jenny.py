import shutil
import os

jenny_paths = [
    os.path.expanduser("~/.local/share/tts/tts_models--en--jenny--jenny"),
    os.path.expanduser("~/.cache/tts/tts_models--en--jenny--jenny"),
    os.path.expanduser("~/AppData/Local/tts/tts_models--en--jenny--jenny"),
    os.path.expanduser("~/AppData/Roaming/tts/tts_models--en--jenny--jenny")
]

for path in jenny_paths:
    if os.path.exists(path):
        shutil.rmtree(path)
        print(f"Deleted {path}")

print("Jenny model cache cleared. Rebecca should work now.")